"""
主题切换弹窗
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QFrame, QGraphicsDropShadowEffect)
from PySide6.QtCore import Qt, Signal, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QColor

from ..config.theme_manager import get_theme_manager


class ThemeCard(QFrame):
    """主题卡片"""
    
    clicked = Signal(str)
    
    def __init__(self, theme_id: str, theme_name: str, is_current: bool = False):
        super().__init__()
        self.theme_id = theme_id
        self.theme_name = theme_name
        self.is_current = is_current
        self.theme_manager = get_theme_manager()
        
        self.setup_ui()
        self.apply_theme()
        
    def setup_ui(self):
        """设置UI"""
        self.setFixedSize(200, 80)
        self.setCursor(Qt.PointingHandCursor)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(5)
        
        # 主题名称
        self.name_label = QLabel(self.theme_name)
        
        # 当前标识
        self.current_label = QLabel("✓ 当前主题" if self.is_current else "")
        
        layout.addWidget(self.name_label)
        layout.addWidget(self.current_label)
        
        self.setLayout(layout)
        
    def apply_theme(self):
        """应用主题"""
        colors = self.theme_manager.get_theme_colors(self.theme_id)
        current_colors = self.theme_manager.get_theme_colors()
        
        # 卡片样式
        if self.is_current:
            self.setStyleSheet(f"""
                ThemeCard {{
                    background-color: {colors['primary']};
                    border: 2px solid {colors['primary']};
                    border-radius: 8px;
                }}
                ThemeCard:hover {{
                    background-color: {colors['hover']};
                }}
            """)
            text_color = colors['background']
        else:
            self.setStyleSheet(f"""
                ThemeCard {{
                    background-color: {current_colors['surface']};
                    border: 1px solid {current_colors['border']};
                    border-radius: 8px;
                }}
                ThemeCard:hover {{
                    background-color: {current_colors['hover']};
                    border-color: {colors['primary']};
                }}
            """)
            text_color = current_colors['text']
        
        # 文本样式
        self.name_label.setStyleSheet(f"""
            QLabel {{
                color: {text_color};
                font-size: 14px;
                font-weight: bold;
                background-color: transparent;
            }}
        """)
        
        self.current_label.setStyleSheet(f"""
            QLabel {{
                color: {text_color};
                font-size: 11px;
                background-color: transparent;
            }}
        """)
        
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.theme_id)
        super().mousePressEvent(event)


class ThemePopup(QWidget):
    """主题切换弹窗"""
    
    theme_selected = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.theme_cards = []
        
        self.setup_ui()
        self.apply_theme()
        
        # 连接主题变化信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
        
    def setup_ui(self):
        """设置UI"""
        self.setWindowFlags(Qt.Popup | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 主容器
        self.container = QFrame()
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 5)
        self.container.setGraphicsEffect(shadow)
        
        # 布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(self.container)
        self.setLayout(main_layout)
        
        # 容器布局
        container_layout = QVBoxLayout()
        container_layout.setContentsMargins(20, 15, 20, 15)
        container_layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("选择主题")
        container_layout.addWidget(title_label)
        
        # 主题卡片容器
        cards_layout = QVBoxLayout()
        cards_layout.setSpacing(8)
        
        # 创建主题卡片
        available_themes = self.theme_manager.get_available_themes()
        current_theme = self.theme_manager.get_current_theme()
        
        for theme_id, theme_name in available_themes.items():
            is_current = (theme_id == current_theme)
            card = ThemeCard(theme_id, theme_name, is_current)
            card.clicked.connect(self.on_theme_card_clicked)
            self.theme_cards.append(card)
            cards_layout.addWidget(card)
        
        container_layout.addLayout(cards_layout)
        self.container.setLayout(container_layout)
        
        # 设置固定大小
        self.setFixedSize(240, 450)
        
    def apply_theme(self):
        """应用主题"""
        colors = self.theme_manager.get_theme_colors()
        
        # 容器样式
        self.container.setStyleSheet(f"""
            QFrame {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 12px;
            }}
        """)
        
        # 标题样式
        if hasattr(self, 'container'):
            title_label = self.container.findChild(QLabel)
            if title_label:
                title_label.setStyleSheet(f"""
                    QLabel {{
                        color: {colors['primary']};
                        font-size: 16px;
                        font-weight: bold;
                        margin-bottom: 5px;
                        background-color: transparent;
                    }}
                """)
        
        # 更新所有主题卡片
        for card in self.theme_cards:
            card.apply_theme()
    
    def on_theme_changed(self, theme_id: str):
        """主题变化处理"""
        self.apply_theme()
        
        # 更新当前主题标识
        for card in self.theme_cards:
            card.is_current = (card.theme_id == theme_id)
            card.apply_theme()
    
    def on_theme_card_clicked(self, theme_id: str):
        """主题卡片点击处理"""
        self.theme_selected.emit(theme_id)
        self.hide()
    
    def show_at_position(self, pos):
        """在指定位置显示弹窗"""
        # 调整位置确保弹窗在屏幕内
        screen_geometry = self.screen().geometry()
        popup_rect = QRect(pos.x() - self.width() + 50, pos.y() + 10, self.width(), self.height())
        
        # 确保弹窗不超出屏幕边界
        if popup_rect.right() > screen_geometry.right():
            popup_rect.moveRight(screen_geometry.right() - 10)
        if popup_rect.bottom() > screen_geometry.bottom():
            popup_rect.moveBottom(screen_geometry.bottom() - 10)
        if popup_rect.left() < screen_geometry.left():
            popup_rect.moveLeft(screen_geometry.left() + 10)
        if popup_rect.top() < screen_geometry.top():
            popup_rect.moveTop(screen_geometry.top() + 10)
        
        self.setGeometry(popup_rect)
        self.show()
        self.raise_()
        self.activateWindow()
