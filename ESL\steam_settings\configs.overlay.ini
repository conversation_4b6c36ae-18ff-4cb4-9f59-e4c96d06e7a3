[overlay::general]
# 1=enable the experimental overlay, might cause crashes
# default=0
enable_experimental_overlay=1
# amount of time to wait before attempting to detect and hook the renderer (DirectX, OpenGL, etc...)
# default=0
hook_delay_sec=5
# timeout for the renderer detector
# default=15
renderer_detector_timeout_sec=15
# 1=disable the achievements notifications
# default=0
disable_achievement_notification=0
# 1=disable friends invitations and messages notifications
# default=0
disable_friend_notification=0
# 1=disable showing notifications for achievements progress
# default=0
disable_achievement_progress=0
# 1=disable any warning in the overlay
# default=0
disable_warning_any=0
# 1=disable the bad app ID warning in the overlay
# default=0
disable_warning_bad_appid=0
# 1=disable the local_save warning in the overlay
# default=0
disable_warning_local_save=0
# by default the overlay will attempt to upload the achievements icons to the GPU
# so that they are displayed, in rare cases this might keep failing and cause FPS drop
# 0=prevent the overlay from attempting to upload the icons periodically,
#   in that case achievements icons win't be displayed
# default=1
upload_achievements_icons_to_gpu=1

[overlay::appearance]
# relative paths will be looked up inside the local folder "steam_settings/fonts" first,
# if that wasn't found, it will be looked up inside the global folder "GSE Settings/settings/fonts"
# default=
Font_Override=Helvetica-Regular.ttf
# global font size
# for built-in font, multiple of 16 is recommended. e.g. 16 32...
# default=16.0
Font_Size=20.0

# achievement icon size
Icon_Size=64.0

# spacing between characters
Font_Glyph_Extra_Spacing_x=1.0
Font_Glyph_Extra_Spacing_y=0.0

# background for all types of notifications
Notification_R=0.12
Notification_G=0.14
Notification_B=0.21
Notification_A=1.0

# notifications corners roundness
Notification_Rounding=10.0
# horizontal (x) and vertical (y) margins for the notifications
Notification_Margin_x=5.0
Notification_Margin_y=5.0

# duration/timing for various notification types (in seconds)
# duration of notification animation in seconds. Set to 0 to disable
Notification_Animation=0.35
# duration of achievement progress indication
Notification_Duration_Progress=6.0
# duration of achievement unlocked
Notification_Duration_Achievement=7.0
# duration of friend invitation
Notification_Duration_Invitation=8.0
# duration of chat message
Notification_Duration_Chat=4.0

# format for the achievement unlock date/time, limited to 79 characters
# if the output formatted string exceeded this limit, the builtin format will be used
# look for the format here: https://en.cppreference.com/w/cpp/chrono/c/strftime
# default=%Y/%m/%d - %H:%M:%S
Achievement_Unlock_Datetime_Format=%Y/%m/%d - %H:%M:%S

# main background when you press shift+tab
Background_R=0.12
Background_G=0.11
Background_B=0.11
Background_A=0.55

Element_R=0.30
Element_G=0.32
Element_B=0.40
Element_A=1.0

ElementHovered_R=0.278
ElementHovered_G=0.393
ElementHovered_B=0.602
ElementHovered_A=1.0

ElementActive_R=-1.0
ElementActive_G=-1.0
ElementActive_B=-1.0
ElementActive_A=-1.0

# ############################# #
# available options:
# top_left
# top_center
# top_right
# bot_left
# bot_center
# bot_right

# position of achievements
PosAchievement=bot_right
# position of invitations
PosInvitation=top_right
# position of chat messages
PosChatMsg=top_center
# ############################# #
